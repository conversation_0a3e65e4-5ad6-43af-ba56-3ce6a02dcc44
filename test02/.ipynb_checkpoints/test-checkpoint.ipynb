{"cells": [{"cell_type": "code", "execution_count": 1, "id": "initial_id", "metadata": {"collapsed": true, "ExecuteTime": {"end_time": "2025-03-03T10:12:04.161331Z", "start_time": "2025-03-03T10:12:03.970590Z"}}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named 'numpy'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mModuleNotFoundError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[1], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[38;5;28;01<PERSON><PERSON>rt\u001b[39;00m \u001b[38;5;21;01mnumpy\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[38;5;21;01mnp\u001b[39;00m\n\u001b[1;32m      2\u001b[0m \u001b[38;5;28;01<PERSON><PERSON>rt\u001b[39;00m \u001b[38;5;21;01mma<PERSON><PERSON><PERSON><PERSON>b\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mpyplot\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[38;5;21;01mplt\u001b[39;00m\n\u001b[1;32m      4\u001b[0m x\u001b[38;5;241m=\u001b[39mnp\u001b[38;5;241m.\u001b[39marange(\u001b[38;5;241m0\u001b[39m,\u001b[38;5;241m2\u001b[39m\u001b[38;5;241m*\u001b[39mnp\u001b[38;5;241m.\u001b[39mpi,\u001b[38;5;241m0.01\u001b[39m)\n", "\u001b[0;31mModuleNotFoundError\u001b[0m: No module named 'numpy'"]}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "x=np.arange(0,2*np.pi,0.01)\n", "y=np.sin(x)\n", "\n", "plt.plot(x,y)\n", "plt.show()"]}, {"cell_type": "code", "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named 'torch'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mModuleNotFoundError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[5], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[38;5;28;01<PERSON><PERSON>rt\u001b[39;00m \u001b[38;5;21;01mtorch\u001b[39;00m\n", "\u001b[0;31mModuleNotFoundError\u001b[0m: No module named 'torch'"]}], "source": ["import torch"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2025-03-03T09:43:30.280118Z", "start_time": "2025-03-03T09:43:30.271056Z"}}, "id": "6c957609008fb5b1", "execution_count": 5}, {"cell_type": "code", "outputs": [{"ename": "NameError", "evalue": "name 'torch' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[6], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m torch\u001b[38;5;241m.\u001b[39mbackends\u001b[38;5;241m.\u001b[39mmps\u001b[38;5;241m.\u001b[39mis_available():\n\u001b[1;32m      2\u001b[0m     mps_device \u001b[38;5;241m=\u001b[39m torch\u001b[38;5;241m.\u001b[39mdevice(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmps\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m      3\u001b[0m     x \u001b[38;5;241m=\u001b[39m torch\u001b[38;5;241m.\u001b[39mones(\u001b[38;5;241m1\u001b[39m, device\u001b[38;5;241m=\u001b[39mmps_device)\n", "\u001b[0;31mNameError\u001b[0m: name 'torch' is not defined"]}], "source": ["if torch.backends.mps.is_available():\n", "    mps_device = torch.device(\"mps\")\n", "    x = torch.ones(1, device=mps_device)\n", "    print (x)\n", "else:\n", "    print (\"MPS device not found.\")"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2025-03-03T09:43:30.959994Z", "start_time": "2025-03-03T09:43:30.949573Z"}}, "id": "459726a33c2c123c", "execution_count": 6}, {"cell_type": "code", "outputs": [], "source": [], "metadata": {"collapsed": false}, "id": "1c217802aa39e689"}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}