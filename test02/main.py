# This is a sample Python script.

# Press ⌃R to execute it or replace it with your code.
# Press Double ⇧ to search everywhere for classes, files, tool windows, actions, and settings.

import random
from typing import Literal
from rich.console import Console
from rich.table import Table


def print_hi(name):
    # Use a breakpoint in the code line below to debug your script.
    print(f"Hi, {name}")  # Press ⌘F8 to toggle the breakpoint.


def test_status_code(code: Literal[200, 400, 401, 403, 404, 500]):
    match code:
        case 200:
            print("OK")
        case 400:
            print("Bad Request")
        case 401:
            print("Unauthorized")
        case 403:
            print("Forbidden")
        case 404:
            print("Not Found")
        case 500:
            print("Internal Server Error")
        case _:
            print("Unknown Status Code")


def str2arr(s: str) -> list:
    arr = list(s)
    print(f"字符串转数组: {arr}")
    print(arr[-2:-6:-1])
    return arr

def double_color_ball() -> bool:
    console = Console()

    n = int(input("生成几注号码: "))
    red_balls = [i for i in range(1, 34)]
    blue_balls = [i for i in range(1, 17)]

    # 创建表格并添加表头
    table = Table(show_header=True)
    for col_name in ("序号", "红球", "蓝球"):
        table.add_column(col_name, justify="center")

    for i in range(n):
        selected_balls = random.sample(red_balls, 6)
        selected_balls.sort()
        blue_ball = random.choice(blue_balls)
        # 向表格中添加行（序号，红色球，蓝色球）
        table.add_row(
            str(i + 1),
            f'[red]{" ".join([f"{ball:0>2d}" for ball in selected_balls])}[/red]',
            f"[blue]{blue_ball:0>2d}[/blue]",
        )

    # 通过控制台输出表格
    console.print(table)
    return True


# Press the green button in the gutter to run the script.
if __name__ == "__main__":
    # print_hi('PyCharm')
    # test_status_code(300)
    # str2arr('hello')

    # try:
    #     items = ['Python', 'Java', 'Java', 'C++', 'Kotlin', 'Python']
    #     print(items.index('Java', 3))
    # except Exception as e:
    #     print(f'异常: {e}')

    # double_color_ball()
    
    s = "hello world"
    # 查找 'o' 从索引 8 开始向前搜索
    position = s.rindex('o', 8)
    print(position)  # 输出 7

# See PyCharm help at https://www.jetbrains.com/help/pycharm/
