import time
from openai import OpenAI


def test_model(model_name, prompt):
    """
    测试指定的OpenAI模型，并返回模型的响应内容和执行时间。

    参数:
    model_name (str): 要测试的模型名称。
    prompt (str): 用于测试模型的输入提示。

    返回:
    tuple: 包含模型响应内容和执行时间的元组。
        - str: 模型的响应内容。
        - float: 执行时间（秒）。
    """
    # 初始化 OpenAI 客户端并传入 API 密钥
    client = OpenAI(api_key="***************************************************")

    # 打印正在测试的模型名称
    print(f"\n=== 测试模型: {model_name} ===")
    # 记录开始时间
    start_time = time.time()

    # 调用 OpenAI 的聊天补全 API 并设置流式传输以获取实时响应
    stream = client.chat.completions.create(
        model=model_name,
        prompt=prompt,
        messages=[{"role": "user", "content": prompt}],
        stream=True,
    )

    # 初始化结果列表用于存储模型的响应内容
    result = []
    # 遍历流式传输的每个数据块
    for chunk in stream:
        # 如果数据块中有内容，则打印并添加到结果列表中
        if chunk.choices[0].delta.content is not None:
            content = chunk.choices[0].delta.content
            print(content, end="")
            result.append(content)

    # 计算模型响应的总时间
    elapsed_time = time.time() - start_time
    # 打印执行时间
    print(f"\n\n执行时间: {elapsed_time:.2f}秒")
    # 返回合并后的响应内容和执行时间
    return "".join(result), elapsed_time


def save_results(results, filename="model_comparison.txt"):
    with open(filename, "w", encoding="utf-8") as f:
        for model, (response, time_taken) in results.items():
            f.write(f"模型: {model}\n")
            f.write(f"响应时间: {time_taken:.2f}秒\n")
            f.write(f"响应内容:\n{response}\n")
            f.write("-" * 50 + "\n")
    print("\n对比结果已保存到", filename)


if __name__ == "__main__":
    prompt = "Say this is a test"
    models = ["gpt-3.5-turbo-0125", "gpt-4"]  # 可以添加更多模型进行对比

    results = {}
    for model in models:
        response, time_taken = test_model(model, prompt)
        results[model] = (response, time_taken)

    save_results(results)
