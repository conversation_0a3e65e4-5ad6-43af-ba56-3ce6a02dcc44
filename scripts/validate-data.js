#!/usr/bin/env node

/**
 * 数据验证脚本
 * 验证结构化数据的完整性、正确性和格式
 */

const fs = require('fs');
const path = require('path');

class DataValidator {
  constructor() {
    this.errors = [];
    this.warnings = [];
    this.data = null;
  }

  /**
   * 主验证方法
   */
  async validate() {
    console.log('🔍 开始数据验证...\n');
    
    try {
      // 加载数据
      await this.loadData();
      
      // 执行各项验证
      this.validateStructure();
      this.validateMetadata();
      this.validateExecutiveSummary();
      this.validateProjectTimeline();
      this.validatePerformanceMetrics();
      this.validateChallengesAndSolutions();
      this.validateFutureRoadmap();
      this.validateDataConsistency();
      
      // 输出结果
      this.outputResults();
      
    } catch (error) {
      console.error('❌ 验证过程中发生错误:', error.message);
      process.exit(1);
    }
  }

  /**
   * 加载数据文件
   */
  async loadData() {
    const dataPath = path.join(__dirname, '../data/structured-content.json');
    
    if (!fs.existsSync(dataPath)) {
      throw new Error('数据文件不存在: ' + dataPath);
    }
    
    try {
      const content = fs.readFileSync(dataPath, 'utf-8');
      this.data = JSON.parse(content);
      console.log('✅ 数据文件加载成功');
    } catch (error) {
      throw new Error('JSON格式错误: ' + error.message);
    }
  }

  /**
   * 验证数据结构
   */
  validateStructure() {
    console.log('📋 验证数据结构...');
    
    const requiredSections = [
      'metadata',
      'executive_summary',
      'project_timeline',
      'performance_metrics',
      'challenges_and_solutions',
      'future_roadmap',
      'summary'
    ];
    
    requiredSections.forEach(section => {
      if (!this.data[section]) {
        this.errors.push(`缺少必需的数据部分: ${section}`);
      }
    });
  }

  /**
   * 验证元数据
   */
  validateMetadata() {
    console.log('📝 验证元数据...');
    
    const metadata = this.data.metadata;
    if (!metadata) return;
    
    const requiredFields = ['title', 'department', 'period', 'report_type'];
    requiredFields.forEach(field => {
      if (!metadata[field]) {
        this.errors.push(`元数据缺少字段: ${field}`);
      }
    });
    
    // 验证日期格式
    if (metadata.creation_date && !this.isValidDate(metadata.creation_date)) {
      this.warnings.push('创建日期格式可能不正确');
    }
  }

  /**
   * 验证执行摘要
   */
  validateExecutiveSummary() {
    console.log('📊 验证执行摘要...');
    
    const summary = this.data.executive_summary;
    if (!summary) return;
    
    // 验证关键指标
    if (!summary.key_metrics) {
      this.errors.push('执行摘要缺少关键指标');
      return;
    }
    
    const expectedMetrics = [
      'okr_achievement_rate',
      'revenue_impact',
      'operational_efficiency',
      'cost_optimization',
      'user_growth'
    ];
    
    expectedMetrics.forEach(metric => {
      if (!summary.key_metrics[metric]) {
        this.warnings.push(`缺少关键指标: ${metric}`);
      } else {
        this.validateMetric(summary.key_metrics[metric], metric);
      }
    });
  }

  /**
   * 验证项目时间线
   */
  validateProjectTimeline() {
    console.log('📅 验证项目时间线...');
    
    const timeline = this.data.project_timeline;
    if (!timeline || !timeline.projects) {
      this.errors.push('项目时间线数据不完整');
      return;
    }
    
    timeline.projects.forEach((project, index) => {
      const requiredFields = ['id', 'name', 'launch_date', 'status', 'key_results', 'business_impact', 'category'];
      requiredFields.forEach(field => {
        if (!project[field]) {
          this.warnings.push(`项目 ${index + 1} 缺少字段: ${field}`);
        }
      });
      
      // 验证状态值
      const validStatuses = ['completed', 'in_progress', 'planned', 'cancelled'];
      if (project.status && !validStatuses.includes(project.status)) {
        this.warnings.push(`项目 ${project.name} 状态值无效: ${project.status}`);
      }
      
      // 验证日期格式
      if (project.launch_date && project.launch_date !== '开发中' && !this.isValidDate(project.launch_date)) {
        this.warnings.push(`项目 ${project.name} 日期格式可能不正确: ${project.launch_date}`);
      }
    });
  }

  /**
   * 验证性能指标
   */
  validatePerformanceMetrics() {
    console.log('📈 验证性能指标...');
    
    const metrics = this.data.performance_metrics;
    if (!metrics) return;
    
    // 验证用户体验改进
    if (metrics.user_experience_improvements) {
      metrics.user_experience_improvements.forEach((improvement, index) => {
        if (!improvement.project || !improvement.description || !improvement.impact) {
          this.warnings.push(`用户体验改进 ${index + 1} 数据不完整`);
        }
      });
    }
    
    // 验证成本节省成果
    if (metrics.cost_reduction_achievements) {
      metrics.cost_reduction_achievements.forEach((achievement, index) => {
        if (!achievement.project || !achievement.cost_savings) {
          this.warnings.push(`成本节省成果 ${index + 1} 数据不完整`);
        }
      });
    }
  }

  /**
   * 验证挑战与解决方案
   */
  validateChallengesAndSolutions() {
    console.log('🎯 验证挑战与解决方案...');
    
    const challenges = this.data.challenges_and_solutions;
    if (!challenges || !challenges.problem_solution_pairs) return;
    
    challenges.problem_solution_pairs.forEach((pair, index) => {
      if (!pair.challenge || !pair.solution) {
        this.errors.push(`挑战解决方案对 ${index + 1} 数据结构不完整`);
        return;
      }
      
      // 验证挑战部分
      const challengeFields = ['title', 'description', 'impact', 'category'];
      challengeFields.forEach(field => {
        if (!pair.challenge[field]) {
          this.warnings.push(`挑战 ${index + 1} 缺少字段: ${field}`);
        }
      });
      
      // 验证解决方案部分
      const solutionFields = ['title', 'description', 'implementation_steps', 'expected_outcome'];
      solutionFields.forEach(field => {
        if (!pair.solution[field]) {
          this.warnings.push(`解决方案 ${index + 1} 缺少字段: ${field}`);
        }
      });
    });
  }

  /**
   * 验证未来路线图
   */
  validateFutureRoadmap() {
    console.log('🚀 验证未来路线图...');
    
    const roadmap = this.data.future_roadmap;
    if (!roadmap || !roadmap.strategic_initiatives) return;
    
    roadmap.strategic_initiatives.forEach((initiative, index) => {
      const requiredFields = ['category', 'priority', 'projects'];
      requiredFields.forEach(field => {
        if (!initiative[field]) {
          this.warnings.push(`战略倡议 ${index + 1} 缺少字段: ${field}`);
        }
      });
      
      // 验证优先级
      const validPriorities = ['high', 'medium', 'low'];
      if (initiative.priority && !validPriorities.includes(initiative.priority)) {
        this.warnings.push(`战略倡议 ${initiative.category} 优先级无效: ${initiative.priority}`);
      }
      
      // 验证项目
      if (initiative.projects) {
        initiative.projects.forEach((project, pIndex) => {
          const projectFields = ['name', 'description', 'expected_outcome', 'timeline'];
          projectFields.forEach(field => {
            if (!project[field]) {
              this.warnings.push(`未来项目 ${initiative.category}-${pIndex + 1} 缺少字段: ${field}`);
            }
          });
        });
      }
    });
  }

  /**
   * 验证数据一致性
   */
  validateDataConsistency() {
    console.log('🔗 验证数据一致性...');
    
    // 验证数值一致性
    const executiveMetrics = this.data.executive_summary?.key_metrics;
    const performanceMetrics = this.data.performance_metrics;
    
    if (executiveMetrics && performanceMetrics) {
      // 检查成本节省数据一致性
      const executiveCostSaving = executiveMetrics.cost_optimization?.value || 0;
      let totalCostSaving = 0;
      
      if (performanceMetrics.cost_reduction_achievements) {
        totalCostSaving = performanceMetrics.cost_reduction_achievements.reduce((sum, achievement) => {
          return sum + (achievement.cost_savings?.value || 0);
        }, 0);
      }
      
      if (executiveCostSaving !== totalCostSaving) {
        this.warnings.push(`成本节省数据不一致: 执行摘要(${executiveCostSaving}万) vs 详细指标(${totalCostSaving}万)`);
      }
    }
  }

  /**
   * 验证单个指标
   */
  validateMetric(metric, name) {
    const requiredFields = ['value', 'unit', 'description'];
    requiredFields.forEach(field => {
      if (metric[field] === undefined || metric[field] === null) {
        this.warnings.push(`指标 ${name} 缺少字段: ${field}`);
      }
    });
    
    // 验证数值类型
    if (typeof metric.value !== 'number') {
      this.warnings.push(`指标 ${name} 的值应为数字类型`);
    }
  }

  /**
   * 验证日期格式
   */
  isValidDate(dateString) {
    // 支持 YYYY-MM-DD 格式
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    return dateRegex.test(dateString) || !isNaN(Date.parse(dateString));
  }

  /**
   * 输出验证结果
   */
  outputResults() {
    console.log('\n📋 验证结果汇总:');
    console.log('================');
    
    if (this.errors.length === 0 && this.warnings.length === 0) {
      console.log('✅ 数据验证通过，未发现问题！');
    } else {
      if (this.errors.length > 0) {
        console.log(`❌ 发现 ${this.errors.length} 个错误:`);
        this.errors.forEach((error, index) => {
          console.log(`   ${index + 1}. ${error}`);
        });
      }
      
      if (this.warnings.length > 0) {
        console.log(`⚠️  发现 ${this.warnings.length} 个警告:`);
        this.warnings.forEach((warning, index) => {
          console.log(`   ${index + 1}. ${warning}`);
        });
      }
    }
    
    console.log('\n📊 数据统计:');
    if (this.data) {
      console.log(`   - 项目总数: ${this.data.project_timeline?.projects?.length || 0}`);
      console.log(`   - 挑战解决方案: ${this.data.challenges_and_solutions?.problem_solution_pairs?.length || 0}`);
      console.log(`   - 未来倡议: ${this.data.future_roadmap?.strategic_initiatives?.length || 0}`);
    }
    
    // 如果有错误，退出时返回错误代码
    if (this.errors.length > 0) {
      process.exit(1);
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const validator = new DataValidator();
  validator.validate();
}

module.exports = DataValidator;
