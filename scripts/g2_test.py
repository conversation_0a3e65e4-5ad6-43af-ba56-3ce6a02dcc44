import streamlit as st
from streamlit_g2 import g2

"""
# Welcome to [streamlit-g2](https://github.com/hustcc/streamlit-g2)!

[G2](https://github.com/antvis/G2) is a visualization grammar for dashboard building, data exploration and storytelling.

This project was created to allow us to render [G2](https://github.com/antvis/G2) charts in streamlit. In the meantime, below are some examples of what you can do with just a few lines of code:
"""

"""
## Bar Chart
"""

options = {
    "autoFit": True,
    "theme": "dark",
    "type": "interval",
    "data": [
        {"genre": "Sports", "sold": 275},
        {"genre": "Strategy", "sold": 115},
        {"genre": "Action", "sold": 120},
        {"genre": "Shooter", "sold": 350},
        {"genre": "Other", "sold": 150},
    ],
    "encode": {
        "x": "genre",
        "y": "sold",
        "color": "genre",
    },
}

g2(options=options)
