#!/usr/bin/env node

/**
 * 数据完整性测试脚本
 * 验证结构化数据与原始PDF内容的一致性
 */

const fs = require('fs');
const path = require('path');

class DataIntegrityTester {
  constructor() {
    this.structuredData = null;
    this.originalData = null;
    this.testResults = [];
  }

  /**
   * 主测试方法
   */
  async test() {
    console.log('🧪 开始数据完整性测试...\n');
    
    try {
      // 加载数据
      await this.loadData();
      
      // 执行测试
      this.testKeyMetrics();
      this.testProjectData();
      this.testPerformanceMetrics();
      this.testChallengesData();
      this.testFutureRoadmap();
      this.testDataCompleteness();
      
      // 输出结果
      this.outputResults();
      
    } catch (error) {
      console.error('❌ 测试过程中发生错误:', error.message);
      process.exit(1);
    }
  }

  /**
   * 加载数据文件
   */
  async loadData() {
    // 加载结构化数据
    const structuredPath = path.join(__dirname, '../data/structured-content.json');
    if (!fs.existsSync(structuredPath)) {
      throw new Error('结构化数据文件不存在');
    }
    
    // 加载原始PDF分析数据
    const originalPath = path.join(__dirname, '../pdf_analysis.json');
    if (!fs.existsSync(originalPath)) {
      throw new Error('原始PDF分析文件不存在');
    }
    
    try {
      this.structuredData = JSON.parse(fs.readFileSync(structuredPath, 'utf-8'));
      this.originalData = JSON.parse(fs.readFileSync(originalPath, 'utf-8'));
      console.log('✅ 数据文件加载成功');
    } catch (error) {
      throw new Error('数据文件格式错误: ' + error.message);
    }
  }

  /**
   * 测试关键指标
   */
  testKeyMetrics() {
    console.log('📊 测试关键指标一致性...');
    
    const metrics = this.structuredData.executive_summary.key_metrics;
    const originalText = this.originalData.content.map(page => page.text).join(' ');
    
    // 测试OKR达成率
    this.assertContains(originalText, '95%', 'OKR达成率');
    this.assertEqual(metrics.okr_achievement_rate.value, 95, 'OKR达成率数值');
    
    // 测试GMV数据
    this.assertContains(originalText, '2700', 'GMV数据');
    this.assertEqual(metrics.revenue_impact.value, 2700, 'GMV数值');
    
    // 测试系统告警减少
    this.assertContains(originalText, '80%', '系统告警减少');
    this.assertEqual(metrics.operational_efficiency.value, 80, '系统告警减少数值');
    
    // 测试成本节省
    this.assertContains(originalText, '26', '成本节省');
    this.assertEqual(metrics.cost_optimization.value, 26, '成本节省数值');
    
    // 测试用户增长
    this.assertContains(originalText, '2 万', '用户增长');
    this.assertEqual(metrics.user_growth.value, 20000, '用户增长数值');
  }

  /**
   * 测试项目数据
   */
  testProjectData() {
    console.log('📅 测试项目数据一致性...');
    
    const projects = this.structuredData.project_timeline.projects;
    const originalText = this.originalData.content.map(page => page.text).join(' ');
    
    // 测试项目名称和日期
    const expectedProjects = [
      { name: '奶卡窜货地址限制', date: '2024-01-04' },
      { name: '京东厂直', date: '2024-02-23' },
      { name: '缺货不拆单', date: '2024-03-21' },
      { name: '计划系统分货改造', date: '2024-03-26' }
    ];
    
    expectedProjects.forEach(expected => {
      this.assertContains(originalText, expected.name, `项目名称: ${expected.name}`);
      this.assertContains(originalText, expected.date, `项目日期: ${expected.date}`);
      
      const project = projects.find(p => p.name === expected.name);
      this.assertNotNull(project, `结构化数据中应包含项目: ${expected.name}`);
      if (project) {
        this.assertEqual(project.launch_date, expected.date, `项目日期一致性: ${expected.name}`);
      }
    });
    
    // 测试项目成果
    this.assertContains(originalText, '65%', '奶卡窜货项目成果');
    this.assertContains(originalText, '300 万', '京东厂直项目成果');
    this.assertContains(originalText, '74 万单', '缺货不拆单项目成果');
  }

  /**
   * 测试性能指标
   */
  testPerformanceMetrics() {
    console.log('📈 测试性能指标一致性...');
    
    const performance = this.structuredData.performance_metrics;
    const originalText = this.originalData.content.map(page => page.text).join(' ');
    
    // 测试成本节省数据
    this.assertContains(originalText, '10 万/年', 'WMS数据库切换节省');
    this.assertContains(originalText, '16 万/年', 'OMS服务器优化节省');
    
    const wmsProject = performance.cost_reduction_achievements.find(a => a.project === 'WMS数据库切换');
    const omsProject = performance.cost_reduction_achievements.find(a => a.project === 'OMS服务器优化');
    
    this.assertNotNull(wmsProject, 'WMS项目应存在');
    this.assertNotNull(omsProject, 'OMS项目应存在');
    
    if (wmsProject) {
      this.assertEqual(wmsProject.cost_savings.value, 10, 'WMS节省金额');
    }
    if (omsProject) {
      this.assertEqual(omsProject.cost_savings.value, 16, 'OMS节省金额');
    }
    
    // 测试性能提升数据
    this.assertContains(originalText, '20%', '寻物流效率优化');
    this.assertContains(originalText, '2 人天', 'POD项目效率提升');
  }

  /**
   * 测试挑战数据
   */
  testChallengesData() {
    console.log('🎯 测试挑战数据一致性...');
    
    const challenges = this.structuredData.challenges_and_solutions.problem_solution_pairs;
    const originalText = this.originalData.content.map(page => page.text).join(' ');
    
    // 测试团队能力短板
    this.assertContains(originalText, '技术疑难问题解决能力较弱', '团队能力短板描述');
    this.assertContains(originalText, '创新意识不足', '创新意识问题');
    
    const capabilityChallenge = challenges.find(c => c.id === 'technical_capability_gap');
    this.assertNotNull(capabilityChallenge, '技术能力挑战应存在');
    
    // 测试解决方案
    this.assertContains(originalText, '加强团队内技术讨论', '解决方案描述');
    this.assertContains(originalText, 'AB 角', 'AB角机制');
  }

  /**
   * 测试未来路线图
   */
  testFutureRoadmap() {
    console.log('🚀 测试未来路线图一致性...');
    
    const roadmap = this.structuredData.future_roadmap.strategic_initiatives;
    const originalText = this.originalData.content.map(page => page.text).join(' ');
    
    // 测试核心项目
    const expectedFutureProjects = [
      '分货性能优化',
      '营销数字化项目落地',
      '拼多多店群对账自动化',
      'AI 应用市场',
      '低代码平台'
    ];
    
    expectedFutureProjects.forEach(project => {
      this.assertContains(originalText, project, `未来项目: ${project}`);
    });
    
    // 验证结构化数据中包含这些项目
    const allProjects = roadmap.flatMap(initiative => initiative.projects.map(p => p.name));
    expectedFutureProjects.forEach(expected => {
      const found = allProjects.some(project => project.includes(expected.replace('AI 应用市场', 'AI应用市场')));
      this.assertTrue(found, `结构化数据应包含未来项目: ${expected}`);
    });
  }

  /**
   * 测试数据完整性
   */
  testDataCompleteness() {
    console.log('🔍 测试数据完整性...');
    
    // 验证所有必需字段都存在
    this.assertNotNull(this.structuredData.metadata, '元数据应存在');
    this.assertNotNull(this.structuredData.executive_summary, '执行摘要应存在');
    this.assertNotNull(this.structuredData.project_timeline, '项目时间线应存在');
    this.assertNotNull(this.structuredData.performance_metrics, '性能指标应存在');
    this.assertNotNull(this.structuredData.challenges_and_solutions, '挑战解决方案应存在');
    this.assertNotNull(this.structuredData.future_roadmap, '未来路线图应存在');
    this.assertNotNull(this.structuredData.summary, '总结应存在');
    
    // 验证数据结构
    this.assertTrue(Array.isArray(this.structuredData.project_timeline.projects), '项目应为数组');
    this.assertTrue(Array.isArray(this.structuredData.challenges_and_solutions.problem_solution_pairs), '挑战解决方案应为数组');
    this.assertTrue(Array.isArray(this.structuredData.future_roadmap.strategic_initiatives), '战略倡议应为数组');
    
    // 验证数据数量合理性
    this.assertTrue(this.structuredData.project_timeline.projects.length >= 4, '项目数量应合理');
    this.assertTrue(this.structuredData.challenges_and_solutions.problem_solution_pairs.length >= 2, '挑战数量应合理');
  }

  // 断言方法
  assertContains(text, substring, description) {
    const result = {
      test: description,
      type: 'contains',
      passed: text.includes(substring),
      expected: `包含 "${substring}"`,
      actual: text.includes(substring) ? '包含' : '不包含'
    };
    this.testResults.push(result);
  }

  assertEqual(actual, expected, description) {
    const result = {
      test: description,
      type: 'equal',
      passed: actual === expected,
      expected: expected,
      actual: actual
    };
    this.testResults.push(result);
  }

  assertNotNull(value, description) {
    const result = {
      test: description,
      type: 'not_null',
      passed: value !== null && value !== undefined,
      expected: '非空值',
      actual: value === null || value === undefined ? '空值' : '非空值'
    };
    this.testResults.push(result);
  }

  assertTrue(condition, description) {
    const result = {
      test: description,
      type: 'true',
      passed: !!condition,
      expected: 'true',
      actual: !!condition
    };
    this.testResults.push(result);
  }

  /**
   * 输出测试结果
   */
  outputResults() {
    console.log('\n📋 测试结果汇总:');
    console.log('================');
    
    const passed = this.testResults.filter(r => r.passed).length;
    const failed = this.testResults.filter(r => !r.passed).length;
    const total = this.testResults.length;
    
    console.log(`总测试数: ${total}`);
    console.log(`通过: ${passed} ✅`);
    console.log(`失败: ${failed} ${failed > 0 ? '❌' : '✅'}`);
    console.log(`通过率: ${Math.round((passed / total) * 100)}%`);
    
    if (failed > 0) {
      console.log('\n❌ 失败的测试:');
      this.testResults.filter(r => !r.passed).forEach((result, index) => {
        console.log(`   ${index + 1}. ${result.test}`);
        console.log(`      期望: ${result.expected}`);
        console.log(`      实际: ${result.actual}`);
      });
    }
    
    if (failed === 0) {
      console.log('\n🎉 所有测试通过！数据完整性验证成功！');
    } else {
      console.log('\n⚠️  存在数据完整性问题，请检查失败的测试项。');
      process.exit(1);
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const tester = new DataIntegrityTester();
  tester.test();
}

module.exports = DataIntegrityTester;
