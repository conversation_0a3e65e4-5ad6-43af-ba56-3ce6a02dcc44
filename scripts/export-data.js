#!/usr/bin/env node

/**
 * 数据导出工具
 * 将结构化数据导出为不同格式，支持web展示和集成
 */

const fs = require('fs');
const path = require('path');

class DataExporter {
  constructor() {
    this.data = null;
    this.outputDir = path.join(__dirname, '../data/exports');
  }

  /**
   * 主导出方法
   */
  async export() {
    console.log('📤 开始数据导出...\n');
    
    try {
      // 加载数据
      await this.loadData();
      
      // 创建输出目录
      this.ensureOutputDir();
      
      // 执行各种导出
      await this.exportForWeb();
      await this.exportForAPI();
      await this.exportSummary();
      await this.exportCSV();
      
      console.log('\n✅ 数据导出完成！');
      
    } catch (error) {
      console.error('❌ 导出过程中发生错误:', error.message);
      process.exit(1);
    }
  }

  /**
   * 加载数据文件
   */
  async loadData() {
    const dataPath = path.join(__dirname, '../data/structured-content.json');
    
    if (!fs.existsSync(dataPath)) {
      throw new Error('数据文件不存在: ' + dataPath);
    }
    
    try {
      const content = fs.readFileSync(dataPath, 'utf-8');
      this.data = JSON.parse(content);
      console.log('✅ 数据文件加载成功');
    } catch (error) {
      throw new Error('JSON格式错误: ' + error.message);
    }
  }

  /**
   * 确保输出目录存在
   */
  ensureOutputDir() {
    if (!fs.existsSync(this.outputDir)) {
      fs.mkdirSync(this.outputDir, { recursive: true });
    }
  }

  /**
   * 导出Web优化格式
   */
  async exportForWeb() {
    console.log('🌐 导出Web优化格式...');
    
    const webData = {
      // 压缩的关键指标，用于首屏快速加载
      quickMetrics: this.extractQuickMetrics(),
      
      // 分页数据，支持懒加载
      sections: {
        dashboard: this.data.executive_summary,
        timeline: this.data.project_timeline,
        performance: this.data.performance_metrics,
        challenges: this.data.challenges_and_solutions,
        roadmap: this.data.future_roadmap
      },
      
      // 搜索索引
      searchIndex: this.buildSearchIndex(),
      
      // 元数据
      metadata: {
        ...this.data.metadata,
        exportTime: new Date().toISOString(),
        version: '1.0.0'
      }
    };
    
    // 保存压缩版本
    const minified = JSON.stringify(webData);
    fs.writeFileSync(
      path.join(this.outputDir, 'web-data.min.json'),
      minified
    );
    
    // 保存格式化版本
    fs.writeFileSync(
      path.join(this.outputDir, 'web-data.json'),
      JSON.stringify(webData, null, 2)
    );
    
    console.log('   ✓ Web数据导出完成');
  }

  /**
   * 导出API格式
   */
  async exportForAPI() {
    console.log('🔌 导出API格式...');
    
    // 分离各个端点的数据
    const apiEndpoints = {
      '/api/summary': {
        data: this.data.executive_summary,
        cache: '5m'
      },
      '/api/projects': {
        data: this.data.project_timeline.projects,
        cache: '1h'
      },
      '/api/metrics': {
        data: this.data.performance_metrics,
        cache: '1h'
      },
      '/api/challenges': {
        data: this.data.challenges_and_solutions.problem_solution_pairs,
        cache: '1d'
      },
      '/api/roadmap': {
        data: this.data.future_roadmap.strategic_initiatives,
        cache: '1d'
      }
    };
    
    // 保存API配置
    fs.writeFileSync(
      path.join(this.outputDir, 'api-endpoints.json'),
      JSON.stringify(apiEndpoints, null, 2)
    );
    
    // 为每个端点创建单独的文件
    for (const [endpoint, config] of Object.entries(apiEndpoints)) {
      const filename = endpoint.replace('/api/', '').replace('/', '-') + '.json';
      fs.writeFileSync(
        path.join(this.outputDir, filename),
        JSON.stringify(config.data, null, 2)
      );
    }
    
    console.log('   ✓ API数据导出完成');
  }

  /**
   * 导出执行摘要
   */
  async exportSummary() {
    console.log('📋 导出执行摘要...');
    
    const summary = {
      title: this.data.metadata.title,
      period: this.data.metadata.period,
      keyAchievements: [
        `OKR达成率: ${this.data.executive_summary.key_metrics.okr_achievement_rate.value}%`,
        `GMV贡献: ${this.data.executive_summary.key_metrics.revenue_impact.value}万`,
        `系统告警减少: ${this.data.executive_summary.key_metrics.operational_efficiency.value}%`,
        `成本节省: ${this.data.executive_summary.key_metrics.cost_optimization.value}万/年`
      ],
      projectsCompleted: this.data.project_timeline.projects.filter(p => p.status === 'completed').length,
      totalProjects: this.data.project_timeline.projects.length,
      challengesAddressed: this.data.challenges_and_solutions.problem_solution_pairs.length,
      futureInitiatives: this.data.future_roadmap.strategic_initiatives.length,
      summary: this.data.summary
    };
    
    // 保存JSON格式
    fs.writeFileSync(
      path.join(this.outputDir, 'executive-summary.json'),
      JSON.stringify(summary, null, 2)
    );
    
    // 保存Markdown格式
    const markdown = this.generateMarkdownSummary(summary);
    fs.writeFileSync(
      path.join(this.outputDir, 'executive-summary.md'),
      markdown
    );
    
    console.log('   ✓ 执行摘要导出完成');
  }

  /**
   * 导出CSV格式
   */
  async exportCSV() {
    console.log('📊 导出CSV格式...');
    
    // 导出项目数据
    const projectsCSV = this.convertProjectsToCSV();
    fs.writeFileSync(
      path.join(this.outputDir, 'projects.csv'),
      projectsCSV
    );
    
    // 导出指标数据
    const metricsCSV = this.convertMetricsToCSV();
    fs.writeFileSync(
      path.join(this.outputDir, 'metrics.csv'),
      metricsCSV
    );
    
    console.log('   ✓ CSV数据导出完成');
  }

  /**
   * 提取快速指标
   */
  extractQuickMetrics() {
    const metrics = this.data.executive_summary.key_metrics;
    return {
      okr: metrics.okr_achievement_rate.value,
      revenue: metrics.revenue_impact.value,
      efficiency: metrics.operational_efficiency.value,
      savings: metrics.cost_optimization.value,
      users: metrics.user_growth.value
    };
  }

  /**
   * 构建搜索索引
   */
  buildSearchIndex() {
    const index = [];
    
    // 索引项目
    this.data.project_timeline.projects.forEach(project => {
      index.push({
        type: 'project',
        id: project.id,
        title: project.name,
        content: project.business_impact,
        keywords: [project.name, project.category, project.status].join(' ').toLowerCase()
      });
    });
    
    // 索引挑战解决方案
    this.data.challenges_and_solutions.problem_solution_pairs.forEach(pair => {
      index.push({
        type: 'challenge',
        id: pair.id,
        title: pair.challenge.title,
        content: pair.solution.description,
        keywords: [pair.challenge.title, pair.solution.title, pair.challenge.category].join(' ').toLowerCase()
      });
    });
    
    return index;
  }

  /**
   * 生成Markdown摘要
   */
  generateMarkdownSummary(summary) {
    return `# ${summary.title}

## 关键成就

${summary.keyAchievements.map(achievement => `- ${achievement}`).join('\n')}

## 项目概况

- 总项目数: ${summary.totalProjects}
- 已完成项目: ${summary.projectsCompleted}
- 完成率: ${Math.round((summary.projectsCompleted / summary.totalProjects) * 100)}%

## 挑战与解决方案

- 已解决挑战: ${summary.challengesAddressed}个

## 未来规划

- 战略倡议: ${summary.futureInitiatives}个

## 总结

**成就**: ${summary.summary.achievements}

**改进方向**: ${summary.summary.areas_for_improvement}

**未来重点**: ${summary.summary.future_focus}

---
*生成时间: ${new Date().toLocaleString('zh-CN')}*
`;
  }

  /**
   * 转换项目数据为CSV
   */
  convertProjectsToCSV() {
    const headers = ['项目ID', '项目名称', '上线时间', '状态', '类别', '业务影响'];
    const rows = [headers.join(',')];
    
    this.data.project_timeline.projects.forEach(project => {
      const row = [
        project.id,
        `"${project.name}"`,
        project.launch_date,
        project.status,
        project.category,
        `"${project.business_impact}"`
      ];
      rows.push(row.join(','));
    });
    
    return rows.join('\n');
  }

  /**
   * 转换指标数据为CSV
   */
  convertMetricsToCSV() {
    const headers = ['指标名称', '数值', '单位', '描述', '趋势'];
    const rows = [headers.join(',')];
    
    Object.entries(this.data.executive_summary.key_metrics).forEach(([key, metric]) => {
      const row = [
        `"${metric.description}"`,
        metric.value,
        metric.unit,
        `"${metric.details}"`,
        metric.trend || ''
      ];
      rows.push(row.join(','));
    });
    
    return rows.join('\n');
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const exporter = new DataExporter();
  exporter.export();
}

module.exports = DataExporter;
