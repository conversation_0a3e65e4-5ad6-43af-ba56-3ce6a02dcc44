# 单行多字段
a = b = c = 1
print(a, b, c) # 结果为 1 1 1

# 单行多类型
a, b, c, d, e = 1, True, "runnoob", [1, 2, 3], 4+1j
print(type(a), type(b), type(c), type(d), type(e)) # 结果为 <class 'int'> <class 'bool'> <class 'str'> <class 'list'> <class 'complex'>

# 类型判断
class Runoob:
    pass
class child(Runoob):
    pass
print(isinstance(Runoob(), Runoob)) # 结果为 True
print(isinstance(child(), Runoob)) # 结果为 True
print(type(child) == Runoob) # 结果为 False

# 类型转换
a = 1
print(type(a))
print(a is True)
print(True == 1)
print(True + 1)
print(issubclass(bool, type(a)))

# 删除变量
del a
# print(a) # NameError: name 'a' is not defined

# 数值运算
print(1 + 1)  # 整数加法，结果为 2
print(1.0 + 1) # 浮点数加法，结果为 2.0
print(3 * 7) # 乘法   
print(3 / 7) # 除法
print(3 // 7) # 整除
print(3 % 7) # 取余
print(3 ** 2) # 幂

# 字符串
str = 'Runoob'  # 定义一个字符串变量
print(str)           # 打印整个字符串，结果为 Runoob
print(str[0:-1])     # 打印字符串第一个到倒数第二个字符（不包含倒数第一个字符），结果为 Runoo
print(str[0])        # 打印字符串的第一个字符，结果为 R
print(str[2:5])      # 打印字符串第三到第五个字符（不包含索引为 5 的字符），结果为 noo
print(str[2:])       # 打印字符串从第三个字符开始到末尾，结果为 noob
print(str * 2)       # 打印字符串两次，结果为 RunoobRunoob
print(str + "TEST")  # 打印字符串和"TEST"拼接在一起，结果为 RunoobTEST
