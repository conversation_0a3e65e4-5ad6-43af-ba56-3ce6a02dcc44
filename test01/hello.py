# 常用工具
print('hello, world!')
# 不换行
print(1 + 1, end=' ')

# 字符串
str = 'hello'
print(str[0])
print(f'步长打印 {str[2:5:2]}')
print(f'反转打印 {str[::-1]}')
print(f'换行打印 hello\nworld')
print('\n')
print(r'转义打印 \n')

# user_input = input('请输入：')  # 接收用户输入
# print(user_input)  # 输出用户输入的内容

# 单行执行
import sys; x = 'runoob'; sys.stdout.write(x + '\n')

# 输出命令行参数
for i in sys.argv:
    print(i)
print('python path is', sys.path, '\n')