#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
火山引擎即梦AI图像生成API测试脚本
基于火山引擎AI中台HTTP请求示例和即梦AI图像生成文档

使用说明：
1. 替换 access_key 和 secret_key 为真实的火山引擎访问凭证
2. 根据需要调整图像生成参数
3. 运行脚本生成图像
"""

import json
import datetime
import hashlib
import hmac
import requests
import base64
import os
from urllib.parse import quote


class VolcengineJimengImageGenerator:
    """火山引擎即梦AI图像生成器"""

    def __init__(self, access_key, secret_key):
        """
        初始化图像生成器

        Args:
            access_key (str): 火山引擎访问密钥ID
            secret_key (str): 火山引擎访问密钥
        """
        self.access_key = access_key
        self.secret_key = secret_key
        self.method = "POST"
        self.host = "visual.volcengineapi.com"
        self.region = "cn-north-1"
        self.endpoint = "https://visual.volcengineapi.com"
        self.service = "cv"
        self.content_type = "application/json"

    def sign(self, key, msg):
        """HMAC-SHA256签名"""
        return hmac.new(key, msg.encode("utf-8"), hashlib.sha256).digest()

    def get_signature_key(self, key, date_stamp, region_name, service_name):
        """获取签名密钥"""
        k_date = self.sign(key.encode("utf-8"), date_stamp)
        k_region = self.sign(k_date, region_name)
        k_service = self.sign(k_region, service_name)
        k_signing = self.sign(k_service, "request")
        return k_signing

    def format_query(self, query_params):
        """格式化查询参数"""
        formatted_params = []
        for key in sorted(query_params.keys()):
            formatted_params.append(
                f"{quote(key, safe='')}={quote(str(query_params[key]), safe='')}"
            )
        return "&".join(formatted_params)

    def sign_v4_request(self, query_string, body):
        """
        执行V4签名请求

        Args:
            query_string (str): 查询字符串
            body (str): 请求体

        Returns:
            dict: 响应结果
        """
        # 创建时间戳
        t = datetime.datetime.utcnow()
        amz_date = t.strftime("%Y%m%dT%H%M%SZ")
        date_stamp = t.strftime("%Y%m%d")

        # 创建规范请求
        canonical_uri = "/"
        canonical_querystring = query_string
        canonical_headers = (
            f"content-type:{self.content_type}\nhost:{self.host}\nx-date:{amz_date}\n"
        )
        signed_headers = "content-type;host;x-date"
        payload_hash = hashlib.sha256(body.encode("utf-8")).hexdigest()
        canonical_request = f"{self.method}\n{canonical_uri}\n{canonical_querystring}\n{canonical_headers}\n{signed_headers}\n{payload_hash}"

        # 创建待签名字符串
        algorithm = "HMAC-SHA256"
        credential_scope = f"{date_stamp}/{self.region}/{self.service}/request"
        string_to_sign = f'{algorithm}\n{amz_date}\n{credential_scope}\n{hashlib.sha256(canonical_request.encode("utf-8")).hexdigest()}'

        # 计算签名
        signing_key = self.get_signature_key(
            self.secret_key, date_stamp, self.region, self.service
        )
        signature = hmac.new(
            signing_key, string_to_sign.encode("utf-8"), hashlib.sha256
        ).hexdigest()

        # 创建授权头
        authorization_header = f"{algorithm} Credential={self.access_key}/{credential_scope}, SignedHeaders={signed_headers}, Signature={signature}"

        # 设置请求头
        headers = {
            "Content-Type": self.content_type,
            "X-Date": amz_date,
            "Authorization": authorization_header,
        }

        # 发送请求
        url = f"{self.endpoint}/?{canonical_querystring}"

        print(f"请求URL: {url}")
        print(f"请求头: {json.dumps(headers, indent=2, ensure_ascii=False)}")
        print(f"请求体: {body}")

        try:
            response = requests.post(url, headers=headers, data=body, timeout=60)
            print(f"响应状态码: {response.status_code}")
            print(f"响应头: {dict(response.headers)}")

            response_data = response.json()
            print(
                f"响应内容: {json.dumps(response_data, indent=2, ensure_ascii=False)}"
            )

            return response_data

        except requests.exceptions.RequestException as e:
            print(f"请求异常: {e}")
            return None
        except json.JSONDecodeError as e:
            print(f"JSON解析异常: {e}")
            print(f"原始响应: {response.text}")
            return None

    def generate_image(self, prompt, **kwargs):
        """
        生成图像

        Args:
            prompt (str): 图像生成提示词
            **kwargs: 其他可选参数
                - seed (int): 随机种子，默认-1
                - width (int): 图像宽度，默认512
                - height (int): 图像高度，默认512
                - use_pre_llm (bool): 是否开启文本扩写，默认True
                - use_sr (bool): 是否使用超分，默认True
                - return_url (bool): 是否返回图片链接，默认True

        Returns:
            dict: API响应结果
        """
        # 查询参数
        query_params = {
            "Action": "CVProcess",
            "Version": "2022-08-31",
        }

        # 请求体参数
        body_params = {
            "req_key": "jimeng_high_aes_general_v21_L",
            "prompt": prompt,
            "seed": kwargs.get("seed", -1),
            "width": kwargs.get("width", 512),
            "height": kwargs.get("height", 512),
            "use_pre_llm": kwargs.get("use_pre_llm", True),
            "use_sr": kwargs.get("use_sr", True),
            "return_url": kwargs.get("return_url", False),
        }

        # 格式化参数
        formatted_query = self.format_query(query_params)
        formatted_body = json.dumps(body_params, ensure_ascii=False)

        print("=" * 60)
        print("开始生成图像...")
        print(f"提示词: {prompt}")
        print(f"参数: {json.dumps(body_params, indent=2, ensure_ascii=False)}")
        print("=" * 60)

        # 执行请求
        return self.sign_v4_request(formatted_query, formatted_body)

    def save_image_from_response(self, response_data, output_dir="generated_images"):
        """
        从响应中保存图像

        Args:
            response_data (dict): API响应数据
            output_dir (str): 输出目录

        Returns:
            list: 保存的文件路径列表
        """
        if not response_data or response_data.get("code") != 10000:
            print("响应数据无效或请求失败")
            return []

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)

        saved_files = []
        data = response_data.get("data", {})

        # 保存base64图像
        base64_images = data.get("binary_data_base64", [])
        for i, base64_data in enumerate(base64_images):
            if base64_data:
                try:
                    image_data = base64.b64decode(base64_data)
                    filename = f"generated_image_{i+1}_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.jpg"
                    filepath = os.path.join(output_dir, filename)

                    with open(filepath, "wb") as f:
                        f.write(image_data)

                    saved_files.append(filepath)
                    print(f"图像已保存: {filepath}")

                except Exception as e:
                    print(f"保存base64图像失败: {e}")

        # 打印图像URL
        image_urls = data.get("image_urls", [])
        if image_urls:
            print("生成的图像URL:")
            for i, url in enumerate(image_urls):
                print(f"  {i+1}. {url}")

        return saved_files


def main():
    """主函数 - 测试图像生成"""

    # Mock的访问凭证 - 请替换为真实的火山引擎凭证
    access_key = "AKLTNzQzYzZjZDFjM2U4NGY3NmFlMzVmN2I0YzhjZmZlNmU"  # Mock AK
    secret_key = (
        "TkdSbE9ESTJPVFEzWVRWbE5EZzRPRGcwTXpObU16YzJPVGswWkRWa1l6WQ=="  # Mock SK
    )

    # 创建图像生成器
    generator = VolcengineJimengImageGenerator(access_key, secret_key)

    # 使用提供的工笔画风格提示词
    prompt = "工笔画风格，三维古风，东方禅意，航拍高角度视角，捕捉了海底极小人物的奔跑追逐；构图大面积留白和丰富的光影，背景以水墨晕染展现水中阳光的多彩折射，现实与虚拟相结合的思考，水墨风格，蓝绿色调，逆光和辉光效果增强冷暖对比，高角度拍摄景深感，整体画面高清，画质通透，发光呈现幽静空灵感"

    # 生成图像参数
    generation_params = {
        "seed": -1,  # 随机种子
        "width": 512,  # 图像宽度
        "height": 512,  # 图像高度
        "use_pre_llm": True,  # 开启文本扩写
        "use_sr": True,  # 开启超分
        "return_url": False,  # 返回base64图像数据而不是链接
    }

    # 执行图像生成
    response = generator.generate_image(prompt, **generation_params)

    # 保存生成的图像
    if response:
        saved_files = generator.save_image_from_response(response)
        if saved_files:
            print(f"\n成功生成并保存了 {len(saved_files)} 张图像")
        else:
            print("\n未能保存图像，请检查响应数据")
    else:
        print("\n图像生成失败")


if __name__ == "__main__":
    main()
