# 即梦AI-图像生成

**来源URL**: https://www.volcengine.com/docs/85621/1537648  
**获取时间**: 2025-06-10 16:37:25 (Asia/Shanghai)  
**最近更新时间**: 2025.04.25 14:40:17  
**首次发布时间**: 2025.04.18 17:01:09  

---

## 接口简介

即梦同款V2.1文生图API，精准解读文字片输入，生成符合语意和有独特审美的图像内容。无缝衔接创意构思与视觉呈现，一键实现「所见即所想」。本服务**重点支持了中英文字符渲染**。

### 常用提示词（prompt）建议

**【艺术风格】+【主体描述】+【文字排版】**

- **艺术风格**：确定画面整体基调，如写实风、插画风，或更具体的风格，如中国水墨风等；
- **画面主体描述**：聚焦主画面内容，例如描述海报中的人物正在做某件事，或具体的物品形态与状态等；
- **文字排版描述**：将文字内容放在双引号""内，同时通过指令描述文字的位置、大小、颜色和风格，精准调整排版效果。

可运用模版格式，根据需求灵活调整描述，当暂时没有明确灵感时，可尝试用简短的描述生成初稿，从中提炼创意，再通过迭代优化提示词，逐步实现理想效果。

## 产品优势

- **文字生成能力**：支持生成准确的中文汉字和英文字母
- **构图**：整体在景别、视角方面变化多样性提升明显，画面层次感增强
- **光影**：去除了刻意的光线表达，画面表现更加真实自然
- **色彩**：画面色调统一性增强，去除了大部分高对比混乱杂色
- **质感**：人物皮肤磨皮感及油腻感减弱，人像、动物及材质类类纹理质感表现更加真实细腻
- **细节丰富度**：简化了过于繁琐的画面元素，画面整体表现现更加具有层次感、秩序感

## Demo展示

| 类型 | prompt | 效果图 |
|------|--------|--------|
| 文字海报 | 制作一张vlog视频封面。马卡龙配色，美女旅游照片+色块的拼贴画风格，主文案是"威海旅游vlog"，副文案是"特种兵一日游 被低估的旅游城市"，海报主体是一个穿着短裙、梳双马尾的少女，人物白色描边 | [效果图] |
| 浪漫水彩 | 工笔画风格，三维古风，东方禅意，航拍高角度视角，捕捉了海底极小人物的奔跑追逐；构图大面积留白和丰富的光影，背景以水墨晕染展现水中阳光的多彩折射，现实与虚拟相结合的思考，水墨风格，蓝绿色调，逆光和辉光效果增强冷暖对比，高角度拍摄景深感，整体画面高清，画质通透，发光呈现幽静空灵感 | [效果图] |
| 胶片梦核 | 过曝，强对比，夜晚，雪地里，巨大的黄色浴缸，小狗泡澡带墨镜，在喝红酒，胶片摄影，毛刺质感，复古滤镜，夜晚，过度曝光，古早，70年代摄影，复古老照片，闪光灯拍摄，闪光灯效果，过曝，过度曝光，闪光灯过曝，极简，高饱和复古色，70s vintage photography, vintage, retro style | [效果图] |

## Prompt建议

### 建议1：提示词结构指南
- 当需要更准确的描述词响应时：**主体描述+风格+美学**
- 当需要更高的美学表现时：**风格+主体描述+美学+氛围**

使用专业的短词语形容风格、镜头语言等美学描述；使用自然语言完整连贯的描述你画面的主体描述(主体+行为+环境)；当输入的提示词较多时，可以把要重点突出的内容放置在最前面。

### 建议2：正向描述
用需要表达的正向提示词替换"不要xxx"这类负面描述词

### 建议3：文字生成控制
- 可以通过明确"文字"生成来强调,帮助模型理解
  - **Before**: 海报,"新年快乐"
  - **After**: 一张海报,上面文字写着:"新年快乐"
- 增加详细的位置、风格描述,可以让文字生成更可控:
  - **Before**: 海报,"新年快乐"
  - **After**: 一张海报,画面上方有手写涂鸦风格的文字写着:"新年快乐"

### 建议4：画面重点特征强调
- 对于复杂内容可以反复强调
  - 例如：御剑飞行->男人站在剑上,他踩在剑上,剑被他踩着,御剑飞仙,飞行
  - 男子向右看->一个头转向右侧的男子,他看向右边
  - 前景虚化->前景中的玫瑰花被模糊,浅景深,前景虚化,前景构图
- 对于美学特征可以反复强调,添加从"哪里"看
  - 例如：大透视、仰视视角->采用低角度拍摄,从下往上,以仰视构图和广角构图的方武
  - 仰视视角->仰视构图,从建筑底部拍摄的视角,低角度拍摄
- 强化视角可以重点描述部分内容
  - 例如：南瓜羹->画面展现一碗百合南瓜羹的一半,米黄色的糯米粉勾芡,块块橙色南瓜在橙色粥中,南瓜丝丝沙沙质感,紫白色百合点缀

## 服务开通

- [开通服务](https://console.volcengine.com/ai/ability/detail/10)

## 请求参数

| 名称 | 内容 |
|------|------|
| 接口地址 | https://visual.volcengineapi.com |
| 请求方式 | POST |
| Content-Type | application/json |

### Query参数

拼接到url后的参数，示例：https://visual.volcengineapi.com?Action=CVProcess&Version=2022-08-31

| 参数 | 可选/必选 | 类型 | 说明 |
|------|-----------|------|------|
| Action | 必选 | String | 接口名，取值：CVProcess |
| Version | 必选 | String | 版本号，取值：2022-08-31 |

### Header参数

> **注意**：本服务固定值：Region为cn-north-1，Service为cv

主要用于鉴权，详见[公共参数](https://www.volcengine.com/docs/6369/67268) - 签名参数 - 在Header中的场景部分

### Body参数

> **注意**：业务请求参数，放到request.body中，MIME-Type为application/json

| 名称 | 必选 | 类型 | 描述 | 备注 |
|------|------|------|------|------|
| req_key | 是 | string | 服务标识，取固定值: jimeng_high_aes_general_v21_L | |
| prompt | 是 | string | 用于生成图像的提示词，中英文均可输入 prompt书写规范参考上文描述 | |
| seed | 可选 | int | 随机种子，作为确定扩散初始状态的基础，默认-1（随机）。若随机种子为相同正整数且其他参数均一致，则生成图片极大概率效果一致 默认值：-1 | |
| width | 可选 | int | 生成图像的宽 默认值：512 取值范围：[256, 768] | 宽、高与512差距过大，则出图效果不佳、延迟过长概率显著增加。超分前建议比例及对应宽高：width*height<br>• 1:1：512*512<br>• 4:3：512*384<br>• 3:4：384*512<br>• 3:2：512*341<br>• 2:3：341*512<br>• 16:9：512*288<br>• 9:16：288*512 |
| height | 可选 | int | 生成图像的高 默认值：512 取值范围：[256, 768] | |
| use_pre_llm | 可选 | bool | 开启文本扩写，会针对输入prompt进行扩写优化，如果输入prompt较短建议开启，如果输入prompt较长建议关闭 默认值：true | 1. prompt过短，如长度小于4时，推荐扩写默认打开，保证出图效果更优；<br>2. prompt较长，如出图4张，可考虑1次关闭扩写，3次打开扩写，保证出图效果多样性 |
| use_sr | 可选 | bool | True：文生图+AIGC超分 False：文生图 默认值：true | 内置的超分功能，开启后可将上述宽高均乘以2返回，此参数打开后延迟会有增加<br>如上述宽高均为512和512，此参数关闭出图 512*512 ，此参数打开出图1024 * 1024 |
| return_url | 可选 | bool | 输出是否返回图片链接（链接有效期为24小时） | |
| logo_info | 可选 | LogoInfo | 水印信息 | |

## 返回参数

### 通用返回参数

请参考[通用返回字段及错误码](https://www.volcengine.com/docs/6444/69728)

### 业务返回参数

> **说明**：重点关注data 字段，其他字段为公共返回

| 字段 | 类型 | 说明 | 备注 |
|------|------|------|------|
| binary_data_base64 | Array of String | 输出处理过的图片Base64数组（单张图） | |
| image_urls | Array of String | 输出处理过的图片url 数组（单张图） | |

## 请求&返回Body部分完整示例

### 请求示例：

```json
{
  "req_key": "jimeng_high_aes_general_v21_L",
  "prompt": "站在树林里"
}
```

### 返回示例：

```json
{
  "code": 10000,
  "data": {
    "algorithm_base_resp": {
      "status_code": 0,
      "status_message": "Success"
    },
    "binary_data_base64": [],
    "image_urls": [
      "https://xxx.jpg"
    ],
    "infer_ctx": {
      "algorithm_key": "high_aes_scheduler_general_v2.1_L",
      "app_key": "2100223643",
      "created_at": "2025-04-22T15:07:11.151025556+08:00",
      "generate_id": "",
      "log_id": "2025042215070738BA47D4CACE51646761",
      "params": {
        "app_id": "",
        "aspect_ratio": "",
        "common_params": "",
        "ddim_steps": 16,
        "edit_session_id": "",
        "fps": 0,
        "frames": 0,
        "group_name": "",
        "height": 512,
        "input_image_url": "",
        "is_only_sr": false,
        "is_pe": false,
        "llm_result": "",
        "media_source": "",
        "n_samples": 0,
        "negative_prompt": "nsfw, nude, low resolution, blurry, worst quality, mutated hands and fingers, poorly drawn face, bad anatomy, distorted hands",
        "ori_prompt": "小男孩在遛狗",
        "origin_request_id": "",
        "output_height": 512,
        "output_width": 512,
        "pe_result": "",
        "predict_tags_result": "",
        "rephraser_result": "现代生活摄影风格，自然光，低角度拍摄。一位小男孩正在遛一只狗；他穿着蓝色T恤和短裤，手中握着牵引绳。小狗是一只金毛寻回犬，显得活泼而友好。他们沿着公园的小路行走，周围是绿色的草地和树木。阳光透过树叶洒下斑驳光影，营造出轻松愉快的氛围，纪实摄影风，低角度视角。",
        "req_key": "high_aes_general_v21_L",
        "rescale": 0,
        "resolution": "",
        "scale": 3.5,
        "seed": -1,
        "shift": 0,
        "sr_img2img_fix_steps": 0,
        "sr_scale": 0,
        "sr_strength": 0.4,
        "sr_upscaler": "",
        "steps": 0,
        "strength": 0.4,
        "trace_id": "",
        "translate_negative_prompt": "nsfw, nude, low resolution, blurry, worst quality, mutated hands and fingers, poorly drawn face, bad anatomy, distorted hands",
        "translate_prompt": "小男孩在遛狗",
        "use_pre_llm": false,
        "use_prompt_aug": false,
        "use_sr": false,
        "version_id": "",
        "video_url": "",
        "vlm_edit": "",
        "vlm_input": "",
        "vlm_output": "",
        "width": 512
      },
      "request_id": "8cd71ccabaea0df425b0ff0ded6269eb020d87cd3300a7f88118a7577806a941",
      "session_id": "",
      "time_stamp": "1745305631"
    },
    "llm_result": "现代生活摄影风格，自然光，低角度拍摄。一位小男孩正在遛一只狗；他穿着蓝色T恤和短裤，手中握着牵引绳。小狗是一只金毛寻回犬，显得活泼而友好。他们沿着公园的小路行走，周围是绿色的草地和树木。阳光透过树叶洒下斑驳光影，营造出轻松愉快的氛围，纪实摄影风，低角度视角。",
    "pe_result": "",
    "predict_tags_result": "",
    "rephraser_result": "现代生活摄影风格，自然光，低角度拍摄。一位小男孩正在遛一只狗；他穿着蓝色T恤和短裤，手中握着牵引绳。小狗是一只金毛寻回犬，显得活泼而友好。他们沿着公园的小路行走，周围是绿色的草地和树木。阳光透过树叶洒下斑驳光影，营造出轻松愉快的氛围，纪实摄影风，低角度视角。",
    "request_id": "8cd71ccabaea0df425b0ff0ded6269eb020d87cd3300a7f88118a7577806a941",
    "vlm_result": ""
  },
  "message": "Success",
  "request_id": "2025042215070738BA47D4CACE51646761",
  "status": 10000,
  "time_elapsed": "4.396915546s"
}
```

## 错误码

### 通用错误码

请参考[通用返回字段及错误码](https://www.volcengine.com/docs/6444/69728)

### 业务错误码

| HttpCode | 错误码 | 错误消息 | 描述 |
|----------|--------|----------|------|
| 200 | 10000 | 无 | 请求成功 |
| 400 | 50511 | Post Img Risk Not Pass | 输出图片后审核未通过 |
| 400 | 50412 | Text Risk Not Pass | 输入文本前审核未通过 |
| 400 | 50413 | Post Text Risk Not Pass | 输入文本因版权风险等原因拦截 |

## 接入说明

### SDK使用说明

请参考[SDK使用说明](https://www.volcengine.com/docs/6444/1340578)

### HTTP方式接入说明

请参考[HTTP请求示例](https://www.volcengine.com/docs/6444/1390583)

## 最佳实践

暂无，待补充

---

*文档获取完成，内容已保存至 test03/volcengine-docs-jimeng-ai-image-generation.md*
