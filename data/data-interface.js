/**
 * 数据接口模块 - 为Executive Dashboard提供结构化数据
 * 支持动态内容加载和响应式布局
 */

class DataInterface {
  constructor() {
    this.data = null;
    this.isLoaded = false;
  }

  /**
   * 异步加载结构化数据
   */
  async loadData() {
    try {
      const response = await fetch('./data/structured-content.json');
      this.data = await response.json();
      this.isLoaded = true;
      return this.data;
    } catch (error) {
      console.error('数据加载失败:', error);
      throw new Error('无法加载数据文件');
    }
  }

  /**
   * 获取执行摘要数据
   */
  getExecutiveSummary() {
    this.checkDataLoaded();
    return this.data.executive_summary;
  }

  /**
   * 获取关键指标数据，支持格式化
   */
  getKeyMetrics(formatted = true) {
    this.checkDataLoaded();
    const metrics = this.data.executive_summary.key_metrics;

    if (!formatted) return metrics;

    // 格式化数值显示
    const formattedMetrics = {};
    for (const [key, metric] of Object.entries(metrics)) {
      formattedMetrics[key] = {
        ...metric,
        displayValue: this.formatValue(metric.value, metric.unit),
        trendIcon: this.getTrendIcon(metric.trend)
      };
    }

    return formattedMetrics;
  }

  /**
   * 获取项目时间线数据
   */
  getProjectTimeline() {
    this.checkDataLoaded();
    return this.data.project_timeline;
  }

  /**
   * 获取按类别分组的项目
   */
  getProjectsByCategory() {
    this.checkDataLoaded();
    const projects = this.data.project_timeline.projects;
    const grouped = {};

    projects.forEach(project => {
      if (!grouped[project.category]) {
        grouped[project.category] = [];
      }
      grouped[project.category].push(project);
    });

    return grouped;
  }

  /**
   * 获取性能指标数据
   */
  getPerformanceMetrics() {
    this.checkDataLoaded();
    return this.data.performance_metrics;
  }

  /**
   * 获取挑战与解决方案数据
   */
  getChallengesAndSolutions() {
    this.checkDataLoaded();
    return this.data.challenges_and_solutions;
  }

  /**
   * 获取未来路线图数据
   */
  getFutureRoadmap() {
    this.checkDataLoaded();
    return this.data.future_roadmap;
  }

  /**
   * 获取按优先级分组的未来项目
   */
  getRoadmapByPriority() {
    this.checkDataLoaded();
    const initiatives = this.data.future_roadmap.strategic_initiatives;
    const grouped = { high: [], medium: [], low: [] };

    initiatives.forEach(initiative => {
      if (grouped[initiative.priority]) {
        grouped[initiative.priority].push(initiative);
      }
    });

    return grouped;
  }

  /**
   * 获取总结数据
   */
  getSummary() {
    this.checkDataLoaded();
    return this.data.summary;
  }

  /**
   * 获取元数据
   */
  getMetadata() {
    this.checkDataLoaded();
    return this.data.metadata;
  }

  /**
   * 搜索功能 - 在所有内容中搜索关键词
   */
  search(keyword) {
    this.checkDataLoaded();
    const results = [];
    const searchText = keyword.toLowerCase();

    // 搜索项目
    this.data.project_timeline.projects.forEach(project => {
      if (project.name.toLowerCase().includes(searchText) ||
        project.business_impact.toLowerCase().includes(searchText)) {
        results.push({
          type: 'project',
          title: project.name,
          content: project.business_impact,
          section: '项目时间线'
        });
      }
    });

    // 搜索挑战与解决方案
    this.data.challenges_and_solutions.problem_solution_pairs.forEach(pair => {
      if (pair.challenge.title.toLowerCase().includes(searchText) ||
        pair.solution.title.toLowerCase().includes(searchText)) {
        results.push({
          type: 'challenge_solution',
          title: pair.challenge.title,
          content: pair.solution.description,
          section: '挑战与解决方案'
        });
      }
    });

    return results;
  }

  /**
   * 数据统计功能
   */
  getStatistics() {
    this.checkDataLoaded();

    return {
      totalProjects: this.data.project_timeline.projects.length,
      completedProjects: this.data.project_timeline.projects.filter(p => p.status === 'completed').length,
      totalCostSavings: this.calculateTotalCostSavings(),
      challengesSolved: this.data.challenges_and_solutions.problem_solution_pairs.length,
      futureInitiatives: this.data.future_roadmap.strategic_initiatives.length
    };
  }

  /**
   * 获取性能指标数据用于图表展示
   */
  getPerformanceMetricsForCharts() {
    this.checkDataLoaded();
    return this.data.performance_metrics;
  }

  /**
   * 格式化告警减少趋势数据
   */
  getAlertReductionTrend() {
    this.checkDataLoaded();

    // 模拟告警数量变化趋势数据
    return {
      labels: ['2024-01', '2024-02', '2024-03', '2024-04', '2024-05', '2024-06'],
      datasets: [{
        label: '系统告警数量',
        data: [100, 85, 70, 45, 30, 20], // 从100减少到20，减少80%
        borderColor: '#dc2626',
        backgroundColor: 'rgba(220, 38, 38, 0.1)',
        tension: 0.4,
        fill: true
      }]
    };
  }

  /**
   * 格式化成本节省数据
   */
  getCostSavingsData() {
    this.checkDataLoaded();

    const costReductions = this.data.performance_metrics.cost_reduction_achievements;

    return {
      labels: costReductions.map(item => item.project),
      datasets: [{
        label: '年度成本节省 (万元)',
        data: costReductions.map(item => item.cost_savings.value),
        backgroundColor: [
          'rgba(34, 197, 94, 0.8)',
          'rgba(59, 130, 246, 0.8)'
        ],
        borderColor: [
          'rgb(34, 197, 94)',
          'rgb(59, 130, 246)'
        ],
        borderWidth: 2
      }]
    };
  }

  /**
   * 格式化系统性能对比数据
   */
  getPerformanceComparisonData() {
    this.checkDataLoaded();

    return {
      labels: ['存储性能', '数据库查询', '内存使用', '系统稳定性', '响应时间'],
      datasets: [{
        label: '优化前',
        data: [30, 40, 80, 60, 70],
        backgroundColor: 'rgba(239, 68, 68, 0.2)',
        borderColor: 'rgb(239, 68, 68)',
        pointBackgroundColor: 'rgb(239, 68, 68)',
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgb(239, 68, 68)'
      }, {
        label: '优化后',
        data: [90, 85, 50, 95, 90],
        backgroundColor: 'rgba(34, 197, 94, 0.2)',
        borderColor: 'rgb(34, 197, 94)',
        pointBackgroundColor: 'rgb(34, 197, 94)',
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgb(34, 197, 94)'
      }]
    };
  }

  /**
   * 格式化图表数据的通用方法
   */
  formatChartData(chartType) {
    this.checkDataLoaded();

    switch (chartType) {
      case 'alertReduction':
        return this.getAlertReductionTrend();
      case 'costSavings':
        return this.getCostSavingsData();
      case 'performanceComparison':
        return this.getPerformanceComparisonData();
      default:
        throw new Error(`未知的图表类型: ${chartType}`);
    }
  }

  // 私有方法
  checkDataLoaded() {
    if (!this.isLoaded || !this.data) {
      throw new Error('数据尚未加载，请先调用 loadData() 方法');
    }
  }

  formatValue(value, unit) {
    if (unit === '万' || unit === '万/年') {
      return `${value.toLocaleString()}${unit}`;
    }
    if (unit === '%') {
      return `${value}%`;
    }
    if (unit === '人') {
      return `${value.toLocaleString()}${unit}`;
    }
    return `${value}${unit}`;
  }

  getTrendIcon(trend) {
    switch (trend) {
      case 'up': return '📈';
      case 'down': return '📉';
      case 'stable': return '➡️';
      default: return '';
    }
  }

  calculateTotalCostSavings() {
    const performanceMetrics = this.data.performance_metrics;
    let total = 0;

    // 计算关键指标中的节省
    total += this.data.executive_summary.key_metrics.cost_optimization.value;

    // 计算性能优化中的节省
    performanceMetrics.cost_reduction_achievements.forEach(achievement => {
      total += achievement.cost_savings.value;
    });

    return total;
  }
}

// 导出数据接口实例
const dataInterface = new DataInterface();

// 如果在浏览器环境中，将实例添加到全局对象
if (typeof window !== 'undefined') {
  window.DataInterface = DataInterface;
  window.dataInterface = dataInterface;
}

// 如果在Node.js环境中，导出模块
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { DataInterface, dataInterface };
}
